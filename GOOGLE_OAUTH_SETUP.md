# 🚀 ACME Corporation Google OAuth Setup

Welcome to the explosive world of ACME authentication! This guide will help you set up Google OAuth for your ACME Corporation Backstage portal.

## 💥 Prerequisites

- Google Cloud Platform account
- ACME Corporation Backstage application (this repo)
- Basic understanding of OAuth 2.0 (or willingness to learn through trial and error, ACME style!)

## 🎯 Step 1: Create Google Cloud Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Name it something ACME-worthy like "acme-corp-backstage" 💣

## 🎪 Step 2: Create OAuth 2.0 Credentials

Following the official Backstage documentation:

1. **Log in to the Google Console**
2. **Select or create a new project** from the dropdown menu on the top bar
3. **Navigate to APIs & Services > Credentials**
4. **Click Create Credentials** and choose **OAuth client ID**
5. **Configure an OAuth consent screen**, if required:
   - For local development, you do not need to enter any Authorized domain
   - For scopes, select `openid`, `auth/userinfo.email` and `auth/userinfo.profile`
   - Add yourself as a test user, if using External user type
   - App name: "ACME Corporation Developer Portal"
   - User support email: your email
   - Developer contact information: your email

6. **Set Application Type to Web Application** with these settings:
   - Name: **ACME Backstage** (or your custom app name)
   - Authorized JavaScript origins: `http://localhost:3000`
   - Authorized Redirect URIs: `http://localhost:7007/api/auth/google/handler/frame`
7. **Click Create**

## 🔧 Step 3: Backend Installation ✅ COMPLETED

Following the official Backstage documentation, the Google auth backend module has been installed and configured:

1. **✅ Google auth backend package installed**:
   ```bash
   yarn --cwd packages/backend add @backstage/plugin-auth-backend-module-google-provider
   ```

2. **✅ Provider added to the backend** in `packages/backend/src/index.ts`:
   ```typescript
   backend.add(import('@backstage/plugin-auth-backend'));
   backend.add(import('@backstage/plugin-auth-backend-module-google-provider'));
   ```

The backend is now ready to handle Google OAuth authentication! 🎯

## 🎨 Step 4: Configure Environment Variables

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Fill in your Google OAuth credentials:
   ```bash
   AUTH_GOOGLE_CLIENT_ID=your_client_id_from_google_console
   AUTH_GOOGLE_CLIENT_SECRET=your_client_secret_from_google_console
   ```

## 🚀 Step 5: Test the Setup ✅ READY!

1. Start your ACME Backstage application:
   ```bash
   yarn start
   ```

2. Navigate to `http://localhost:3000`
3. You should see the explosive ACME sign-in page with both GitHub and Google options! 💥
4. Click the Google button and test the authentication flow

**Status**: ✅ Backend and frontend are running successfully with Google auth provider configured!

## 🎭 Step 6: User Entity Configuration ⚠️ IMPORTANT!

**If you get "Failed to sign-in, unable to resolve user identity" error**, you need to add your Google email to the user catalog.

### Quick Fix:

1. **Open**: `examples/org.yaml`
2. **Find the InduwaraSMPN user** (around line 85)
3. **Replace `<EMAIL>`** with your actual Google email:
   ```yaml
   annotations:
     google.com/email: <EMAIL>
   ```
4. **Restart the server**: `yarn start`

### Detailed Instructions:
See `USER_SETUP_GUIDE.md` for complete step-by-step instructions on adding users and troubleshooting sign-in issues.

## 🎪 Troubleshooting

### "Error 400: redirect_uri_mismatch"
- Check that your redirect URI in Google Console exactly matches the one Backstage is using
- Make sure you're using the correct port (7007 for backend, 3000 for frontend)

### "Error 403: access_denied"
- Verify your OAuth consent screen is properly configured
- Check that the user's email is added as a test user (if in testing mode)
- Ensure the Google+ API is enabled

### Users can sign in but aren't recognized
- Check that user entities exist in your catalog
- Verify the `google.com/email` annotation matches the user's Google email
- Review the resolver configuration in `app-config.yaml`

## 🎯 Production Considerations

1. **OAuth Consent Screen**: Submit for verification if you need more than 100 users
2. **Redirect URIs**: Update with your production domain
3. **Environment Variables**: Use secure secret management in production
4. **User Provisioning**: Consider automatic user creation or LDAP integration

## 💣 ACME-Style Success!

Once everything is working, your users will be able to sign in with their Google accounts and experience the full explosive power of the ACME Corporation Developer Portal!

Remember: With great power comes great responsibility... and occasional cartoon-style explosions! 🎪💥

---

*This setup guide was crafted with the precision of an ACME blueprint and the reliability of... well, let's just say it should work better than most ACME products!* 😄
