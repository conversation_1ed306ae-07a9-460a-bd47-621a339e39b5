import { createUnifiedTheme } from '@backstage/theme';

// Acme Corporation Color Palette
const acmeColors = {
  primary: '#EF4034',    // Acme Red
  black: '#000000',      // Acme Black
  white: '#FFFFFF',      // Acme White
  yellow: '#FFD700',     // Accent Yellow for warnings/special elements
  blue: '#00BFFF',       // Electric Blue for secondary elements
};

// Custom Acme theme extending Backstage theme
export const acmeTheme = createUnifiedTheme({
  palette: {
    mode: 'light',
    primary: {
      main: acmeColors.primary,
      dark: '#D63027',
      light: '#F26B5C',
      contrastText: acmeColors.white,
    },
    secondary: {
      main: acmeColors.blue,
      dark: '#0099CC',
      light: '#33CCFF',
      contrastText: acmeColors.white,
    },
    error: {
      main: '#f44336',
      dark: '#d32f2f',
      light: '#e57373',
      contrastText: acmeColors.white,
    },
    warning: {
      main: acmeColors.yellow,
      dark: '#E6C200',
      light: '#FFDD33',
      contrastText: acmeColors.black,
    },
    info: {
      main: acmeColors.blue,
      dark: '#0099CC',
      light: '#33CCFF',
      contrastText: acmeColors.white,
    },
    success: {
      main: '#4CAF50',
      dark: '#388E3C',
      light: '#81C784',
      contrastText: acmeColors.white,
    },
    background: {
      default: acmeColors.white,
      paper: acmeColors.white,
    },
    text: {
      primary: acmeColors.black,
      secondary: '#666666',
    },
  },
  defaultPageTheme: 'home',
  fontFamily: '"Helvetica", "Arial", sans-serif',
});
