/* ACME Corporation Custom Styles */
/* Bringing cartoon chaos to the developer portal! */

/* Global animations */
@keyframes wiggle {
  0% { transform: rotate(0deg); }
  25% { transform: rotate(1deg); }
  75% { transform: rotate(-1deg); }
  100% { transform: rotate(0deg); }
}

@keyframes bounce {
  0%, 20%, 60%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  80% {
    transform: translateY(-5px);
  }
}

@keyframes poof {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes explode {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(239, 64, 52, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(239, 64, 52, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(239, 64, 52, 0);
  }
}

/* Acme-style buttons */
.acme-button {
  position: relative;
  background: linear-gradient(45deg, #EF4034 30%, #F26B5C 90%);
  border: 3px solid #000000;
  border-radius: 8px;
  color: #FFFFFF;
  font-family: 'Bangers', cursive;
  font-size: 1.1rem;
  padding: 12px 24px;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 4px 4px 0px #000000;
}

.acme-button:hover {
  animation: wiggle 0.5s ease-in-out;
  transform: translateY(-2px);
  box-shadow: 6px 6px 0px #000000;
}

.acme-button:active {
  animation: explode 0.6s ease-out;
  transform: translateY(1px);
  box-shadow: 2px 2px 0px #000000;
}

/* Cartoon-style cards */
.acme-card {
  border: 3px solid #000000;
  border-radius: 12px;
  background: #FFFFFF;
  box-shadow: 6px 6px 0px #000000;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.acme-card:hover {
  animation: bounce 0.6s ease-in-out;
  box-shadow: 8px 8px 0px #000000;
  transform: translateY(-4px);
}

.acme-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #EF4034, #FFD700, #00BFFF, #EF4034);
  border-radius: 12px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.acme-card:hover::before {
  opacity: 0.3;
}

/* Loading spinner with cartoon bomb */
.acme-loading {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid #000000;
  border-radius: 50%;
  border-top: 4px solid #EF4034;
  animation: spin 1s linear infinite;
  position: relative;
}

.acme-loading::after {
  content: '💣';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 20px;
  animation: wiggle 0.5s ease-in-out infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Sidebar enhancements */
.acme-sidebar-item {
  transition: all 0.2s ease;
  border-radius: 8px;
  margin: 4px 8px;
  position: relative;
}

.acme-sidebar-item:hover {
  background: rgba(239, 64, 52, 0.1);
  transform: translateX(8px);
  border-left: 4px solid #EF4034;
}

.acme-sidebar-item:hover::before {
  content: '💥';
  position: absolute;
  left: -20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  animation: poof 0.5s ease-in-out;
}

/* Header styling */
.acme-header {
  background: linear-gradient(90deg, #EF4034 0%, #F26B5C 50%, #FFD700 100%);
  border-bottom: 4px solid #000000;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  position: relative;
}

.acme-header::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  right: 0;
  height: 4px;
  background: repeating-linear-gradient(
    90deg,
    #000000 0px,
    #000000 10px,
    #FFFFFF 10px,
    #FFFFFF 20px
  );
}

/* Error messages with humor */
.acme-error {
  background: linear-gradient(45deg, #EF4034, #FF6B6B);
  border: 3px solid #000000;
  border-radius: 12px;
  color: #FFFFFF;
  padding: 16px;
  margin: 16px 0;
  font-family: 'Bangers', cursive;
  font-size: 1.1rem;
  position: relative;
  box-shadow: 4px 4px 0px #000000;
}

.acme-error::before {
  content: '💥 KABOOM! ';
  font-size: 1.2rem;
}

/* Success messages */
.acme-success {
  background: linear-gradient(45deg, #4CAF50, #81C784);
  border: 3px solid #000000;
  border-radius: 12px;
  color: #FFFFFF;
  padding: 16px;
  margin: 16px 0;
  font-family: 'Bangers', cursive;
  font-size: 1.1rem;
  position: relative;
  box-shadow: 4px 4px 0px #000000;
}

.acme-success::before {
  content: '🎉 SUCCESS! ';
  font-size: 1.2rem;
}

/* Warning messages */
.acme-warning {
  background: linear-gradient(45deg, #FFD700, #FFEB3B);
  border: 3px solid #000000;
  border-radius: 12px;
  color: #000000;
  padding: 16px;
  margin: 16px 0;
  font-family: 'Bangers', cursive;
  font-size: 1.1rem;
  position: relative;
  box-shadow: 4px 4px 0px #000000;
}

.acme-warning::before {
  content: '⚠️ DANGER! ';
  font-size: 1.2rem;
}

/* Table styling */
.acme-table {
  border: 3px solid #000000;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 4px 4px 0px #000000;
}

.acme-table th {
  background: linear-gradient(45deg, #EF4034, #F26B5C);
  color: #FFFFFF;
  font-family: 'Bangers', cursive;
  font-size: 1.1rem;
  text-transform: uppercase;
  border-bottom: 2px solid #000000;
  padding: 12px;
}

.acme-table td {
  border-bottom: 1px solid #000000;
  padding: 12px;
  transition: background-color 0.2s ease;
}

.acme-table tr:hover td {
  background: rgba(239, 64, 52, 0.1);
}

/* Form inputs */
.acme-input {
  border: 3px solid #000000;
  border-radius: 8px;
  padding: 12px;
  font-family: 'Helvetica', 'Arial', sans-serif;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #FFFFFF;
}

.acme-input:focus {
  outline: none;
  border-color: #EF4034;
  box-shadow: 0 0 0 3px rgba(239, 64, 52, 0.2);
  animation: wiggle 0.3s ease-in-out;
}

/* Utility classes */
.acme-text-primary {
  color: #EF4034;
  font-family: 'Bangers', cursive;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.acme-text-secondary {
  color: #000000;
  font-family: 'Helvetica', 'Arial', sans-serif;
}

.acme-shadow {
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.2);
}

.acme-border {
  border: 3px solid #000000;
  border-radius: 8px;
}

/* Main content area */
.acme-main-content {
  background: linear-gradient(135deg, #FFFFFF 0%, #F8F8F8 100%);
  min-height: 100vh;
  position: relative;
}

.acme-main-content::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(239, 64, 52, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(0, 191, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* Sidebar styling */
.acme-sidebar {
  background: linear-gradient(180deg, #FFFFFF 0%, #F5F5F5 100%);
  border-right: 4px solid #000000;
}

/* Page headers */
.acme-page-header {
  background: linear-gradient(90deg, #EF4034 0%, #F26B5C 100%);
  color: #FFFFFF;
  padding: 24px;
  margin-bottom: 24px;
  border: 3px solid #000000;
  border-radius: 12px;
  box-shadow: 6px 6px 0px #000000;
  font-family: 'Luckiest Guy', cursive;
  font-size: 2rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  position: relative;
  overflow: hidden;
}

.acme-page-header::after {
  content: '💥';
  position: absolute;
  top: 50%;
  right: 24px;
  transform: translateY(-50%);
  font-size: 2rem;
  animation: bounce 2s ease-in-out infinite;
}

/* Navigation breadcrumbs */
.acme-breadcrumb {
  background: rgba(239, 64, 52, 0.1);
  border: 2px solid #EF4034;
  border-radius: 20px;
  padding: 8px 16px;
  margin: 8px 0;
  font-family: 'Bangers', cursive;
  color: #EF4034;
  display: inline-block;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .acme-button {
    font-size: 1rem;
    padding: 10px 20px;
  }

  .acme-card {
    margin: 8px;
  }

  .acme-sidebar-item:hover {
    transform: translateX(4px);
  }

  .acme-page-header {
    font-size: 1.5rem;
    padding: 16px;
  }

  .acme-page-header::after {
    font-size: 1.5rem;
    right: 16px;
  }
}
