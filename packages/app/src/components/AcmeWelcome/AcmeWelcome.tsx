import {
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Grid,
  Box,
  makeStyles
} from '@material-ui/core';
import LaunchIcon from '@material-ui/icons/Launch';
import BuildIcon from '@material-ui/icons/Build';
import ExploreIcon from '@material-ui/icons/Explore';
import StarIcon from '@material-ui/icons/Star';

const useStyles = makeStyles((theme) => ({
  welcomeContainer: {
    padding: theme.spacing(4),
    background: 'linear-gradient(135deg, #FFFFFF 0%, #F8F8F8 100%)',
    minHeight: '80vh',
    position: 'relative',
  },
  heroCard: {
    background: 'linear-gradient(45deg, #EF4034 30%, #F26B5C 90%)',
    color: '#FFFFFF',
    border: '4px solid #000000',
    borderRadius: '16px',
    boxShadow: '8px 8px 0px #000000',
    marginBottom: theme.spacing(4),
    position: 'relative',
    overflow: 'hidden',
    '&:hover': {
      transform: 'translateY(-4px)',
      boxShadow: '12px 12px 0px #000000',
      transition: 'all 0.3s ease',
    },
  },
  heroContent: {
    padding: theme.spacing(4),
    textAlign: 'center',
    position: 'relative',
    zIndex: 2,
  },
  heroTitle: {
    fontFamily: '"Luckiest Guy", cursive',
    fontSize: '3rem',
    textShadow: '3px 3px 6px rgba(0, 0, 0, 0.5)',
    marginBottom: theme.spacing(2),
    animation: '$bounce 2s ease-in-out infinite',
  },
  heroSubtitle: {
    fontFamily: '"Bangers", cursive',
    fontSize: '1.5rem',
    textShadow: '2px 2px 4px rgba(0, 0, 0, 0.3)',
    marginBottom: theme.spacing(3),
  },
  featureCard: {
    border: '3px solid #000000',
    borderRadius: '12px',
    background: '#FFFFFF',
    boxShadow: '6px 6px 0px #000000',
    transition: 'all 0.3s ease',
    height: '100%',
    '&:hover': {
      transform: 'translateY(-4px) rotate(1deg)',
      boxShadow: '8px 8px 0px #000000',
      animation: '$wiggle 0.5s ease-in-out',
    },
  },
  featureIcon: {
    fontSize: '3rem',
    color: '#EF4034',
    marginBottom: theme.spacing(2),
    filter: 'drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3))',
  },
  featureTitle: {
    fontFamily: '"Bangers", cursive',
    fontSize: '1.5rem',
    color: '#000000',
    marginBottom: theme.spacing(1),
  },
  featureDescription: {
    fontFamily: '"Helvetica", "Arial", sans-serif',
    color: '#333333',
    lineHeight: 1.6,
  },
  ctaButton: {
    background: 'linear-gradient(45deg, #FFD700 30%, #FFEB3B 90%)',
    border: '3px solid #000000',
    borderRadius: '8px',
    color: '#000000',
    fontFamily: '"Bangers", cursive',
    fontSize: '1.2rem',
    padding: theme.spacing(1.5, 4),
    textTransform: 'uppercase',
    fontWeight: 'bold',
    boxShadow: '4px 4px 0px #000000',
    '&:hover': {
      background: 'linear-gradient(45deg, #E6C200 30%, #FFD700 90%)',
      transform: 'translateY(-2px)',
      boxShadow: '6px 6px 0px #000000',
      animation: '$explode 0.6s ease-out',
    },
  },
  explosionEffect: {
    position: 'absolute',
    top: '10%',
    right: '10%',
    fontSize: '4rem',
    animation: '$float 3s ease-in-out infinite',
    zIndex: 1,
  },
  '@keyframes bounce': {
    '0%, 20%, 60%, 100%': {
      transform: 'translateY(0)',
    },
    '40%': {
      transform: 'translateY(-10px)',
    },
    '80%': {
      transform: 'translateY(-5px)',
    },
  },
  '@keyframes wiggle': {
    '0%': { transform: 'rotate(0deg)' },
    '25%': { transform: 'rotate(1deg)' },
    '75%': { transform: 'rotate(-1deg)' },
    '100%': { transform: 'rotate(0deg)' },
  },
  '@keyframes explode': {
    '0%': {
      transform: 'scale(1)',
      boxShadow: '0 0 0 rgba(255, 215, 0, 0.7)',
    },
    '70%': {
      transform: 'scale(1.05)',
      boxShadow: '0 0 0 10px rgba(255, 215, 0, 0)',
    },
    '100%': {
      transform: 'scale(1)',
      boxShadow: '0 0 0 0 rgba(255, 215, 0, 0)',
    },
  },
  '@keyframes float': {
    '0%, 100%': {
      transform: 'translateY(0px) rotate(0deg)',
    },
    '50%': {
      transform: 'translateY(-20px) rotate(10deg)',
    },
  },
}));

const features = [
  {
    icon: <LaunchIcon />,
    title: 'Launch Projects',
    description: 'Deploy your ideas faster than a cartoon rocket! Our scaffolding tools will have you up and running in no time.',
  },
  {
    icon: <BuildIcon />,
    title: 'Build & Create',
    description: 'Construct amazing software with our explosive development tools. Warning: May cause uncontrollable innovation!',
  },
  {
    icon: <ExploreIcon />,
    title: 'Explore APIs',
    description: 'Discover and document APIs with the precision of an ACME blueprint. No anvils required!',
  },
  {
    icon: <StarIcon />,
    title: 'Manage Teams',
    description: 'Organize your development teams like a well-oiled cartoon machine. Results may vary wildly!',
  },
];

export const AcmeWelcome = () => {
  const classes = useStyles();

  return (
    <Box className={classes.welcomeContainer}>
      <div className={classes.explosionEffect}>💥</div>
      
      <Card className={classes.heroCard}>
        <CardContent className={classes.heroContent}>
          <Typography variant="h1" className={classes.heroTitle}>
            Welcome to ACME Corp!
          </Typography>
          <Typography variant="h2" className={classes.heroSubtitle}>
            Where Innovation Meets Cartoon Chaos! 🚀
          </Typography>
          <Typography variant="body1" style={{ fontSize: '1.2rem', marginBottom: '24px' }}>
            Your one-stop developer portal for explosive productivity and wildly successful projects.
            <br />
            <em>(Side effects may include: increased efficiency, uncontrollable creativity, and occasional anvil drops)</em>
          </Typography>
          <Button className={classes.ctaButton}>
            Start Building! 💣
          </Button>
        </CardContent>
      </Card>

      <Grid container spacing={4}>
        {features.map((feature, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card className={classes.featureCard}>
              <CardContent style={{ textAlign: 'center', padding: '24px' }}>
                <div className={classes.featureIcon}>
                  {feature.icon}
                </div>
                <Typography variant="h3" className={classes.featureTitle}>
                  {feature.title}
                </Typography>
                <Typography variant="body2" className={classes.featureDescription}>
                  {feature.description}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};
