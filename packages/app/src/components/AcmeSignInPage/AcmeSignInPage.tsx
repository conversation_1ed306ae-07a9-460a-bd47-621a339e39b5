import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  makeStyles,
} from '@material-ui/core';
import { SignInProviderConfig } from '@backstage/core-components';
import { useApi, githubAuthApiRef, googleAuthApiRef } from '@backstage/core-plugin-api';

interface AcmeSignInPageProps {
  providers?: SignInProviderConfig[];
}

const useStyles = makeStyles((theme) => ({
  container: {
    minHeight: '100vh',
    background: 'linear-gradient(135deg, #EF4034 0%, #F26B5C 50%, #FFD700 100%)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing(2),
    position: 'relative',
    overflow: 'hidden',
  },
  backgroundEffect: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundImage: `
      radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(0, 0, 0, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(255, 215, 0, 0.1) 0%, transparent 50%)
    `,
    animation: '$float 6s ease-in-out infinite',
  },
  signInCard: {
    maxWidth: 500,
    width: '100%',
    background: '#FFFFFF',
    border: '4px solid #000000',
    borderRadius: '16px',
    boxShadow: '12px 12px 0px #000000',
    position: 'relative',
    zIndex: 2,
    animation: '$bounce 2s ease-in-out infinite',
  },
  cardContent: {
    padding: theme.spacing(4),
    textAlign: 'center',
  },
  logo: {
    fontSize: '4rem',
    marginBottom: theme.spacing(2),
    animation: '$wiggle 3s ease-in-out infinite',
  },
  title: {
    fontFamily: '"Luckiest Guy", cursive',
    fontSize: '2.5rem',
    color: '#EF4034',
    textShadow: '2px 2px 4px rgba(0, 0, 0, 0.3)',
    marginBottom: theme.spacing(1),
  },
  subtitle: {
    fontFamily: '"Bangers", cursive',
    fontSize: '1.2rem',
    color: '#000000',
    marginBottom: theme.spacing(3),
  },
  description: {
    fontFamily: '"Helvetica", "Arial", sans-serif',
    color: '#333333',
    marginBottom: theme.spacing(4),
    lineHeight: 1.6,
  },
  providerButton: {
    width: '100%',
    marginBottom: theme.spacing(2),
    padding: theme.spacing(1.5),
    border: '3px solid #000000',
    borderRadius: '8px',
    fontFamily: '"Bangers", cursive',
    fontSize: '1.1rem',
    textTransform: 'uppercase',
    fontWeight: 'bold',
    boxShadow: '4px 4px 0px #000000',
    transition: 'all 0.3s ease',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: '6px 6px 0px #000000',
      animation: '$explode 0.6s ease-out',
    },
  },
  githubButton: {
    background: 'linear-gradient(45deg, #333333 30%, #555555 90%)',
    color: '#FFFFFF',
    '&:hover': {
      background: 'linear-gradient(45deg, #222222 30%, #333333 90%)',
    },
  },
  googleButton: {
    background: 'linear-gradient(45deg, #4285F4 30%, #34A853 90%)',
    color: '#FFFFFF',
    '&:hover': {
      background: 'linear-gradient(45deg, #3367D6 30%, #2E7D32 90%)',
    },
  },
  footer: {
    position: 'absolute',
    bottom: theme.spacing(2),
    left: '50%',
    transform: 'translateX(-50%)',
    color: '#FFFFFF',
    fontFamily: '"Bangers", cursive',
    fontSize: '1rem',
    textShadow: '2px 2px 4px rgba(0, 0, 0, 0.5)',
    textAlign: 'center',
  },
  explosionIcon: {
    position: 'absolute',
    top: theme.spacing(2),
    right: theme.spacing(2),
    fontSize: '2rem',
    animation: '$float 4s ease-in-out infinite',
  },
  '@keyframes bounce': {
    '0%, 20%, 60%, 100%': {
      transform: 'translateY(0)',
    },
    '40%': {
      transform: 'translateY(-10px)',
    },
    '80%': {
      transform: 'translateY(-5px)',
    },
  },
  '@keyframes wiggle': {
    '0%, 100%': { transform: 'rotate(0deg)' },
    '25%': { transform: 'rotate(2deg)' },
    '75%': { transform: 'rotate(-2deg)' },
  },
  '@keyframes explode': {
    '0%': {
      transform: 'scale(1)',
      boxShadow: '0 0 0 rgba(239, 64, 52, 0.7)',
    },
    '70%': {
      transform: 'scale(1.05)',
      boxShadow: '0 0 0 10px rgba(239, 64, 52, 0)',
    },
    '100%': {
      transform: 'scale(1)',
      boxShadow: '0 0 0 0 rgba(239, 64, 52, 0)',
    },
  },
  '@keyframes float': {
    '0%, 100%': {
      transform: 'translateY(0px) rotate(0deg)',
    },
    '50%': {
      transform: 'translateY(-20px) rotate(10deg)',
    },
  },
}));

export const AcmeSignInPage = ({ providers = [] }: AcmeSignInPageProps) => {
  const classes = useStyles();
  const githubAuthApi = useApi(githubAuthApiRef);
  const googleAuthApi = useApi(googleAuthApiRef);

  const handleSignIn = async (providerId: string) => {
    try {
      if (providerId === 'github-auth-provider') {
        await githubAuthApi.signIn();
      } else if (providerId === 'google-auth-provider') {
        await googleAuthApi.signIn();
      }
    } catch (error) {
      // Handle sign-in error silently or show user-friendly message
      // eslint-disable-next-line no-console
      console.error('Sign-in failed:', error);
    }
  };

  const getProviderButton = (provider: SignInProviderConfig) => {
    const isGithub = provider.id === 'github-auth-provider';
    const isGoogle = provider.id === 'google-auth-provider';

    let buttonClass = classes.providerButton;
    let startIcon = '🚀';
    let endIcon = '🎯';

    if (isGithub) {
      buttonClass += ` ${classes.githubButton}`;
      startIcon = '🐙';
      endIcon = '💻';
    } else if (isGoogle) {
      buttonClass += ` ${classes.googleButton}`;
      startIcon = '🔍';
      endIcon = '📧';
    }

    return (
      <Button
        key={provider.id}
        className={buttonClass}
        onClick={() => handleSignIn(provider.id)}
        startIcon={startIcon}
      >
        {provider.title} {endIcon}
      </Button>
    );
  };

  return (
    <Box className={classes.container}>
      <div className={classes.backgroundEffect} />
      <div className={classes.explosionIcon}>💥</div>
      
      <Card className={classes.signInCard}>
        <CardContent className={classes.cardContent}>
          <div className={classes.logo}>🏢</div>
          
          <Typography variant="h1" className={classes.title}>
            ACME CORP
          </Typography>
          
          <Typography variant="h2" className={classes.subtitle}>
            Developer Portal Access
          </Typography>
          
          <Typography variant="body1" className={classes.description}>
            Welcome to the ACME Corporation Developer Portal! 
            <br />
            Sign in to access our explosive collection of development tools, 
            APIs, and services. 
            <br />
            <em>(Warning: May cause increased productivity and uncontrollable innovation!)</em>
          </Typography>

          <Grid container spacing={2}>
            {providers.map((provider) => (
              <Grid item xs={12} key={provider.id}>
                {getProviderButton(provider)}
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      <div className={classes.footer}>
        <div>🎯 ACME Corporation - Where Innovation Explodes! 💣</div>
        <div style={{ fontSize: '0.8rem', marginTop: '8px' }}>
          Powered by Backstage • Built with Cartoon Chaos
        </div>
      </div>
    </Box>
  );
};
