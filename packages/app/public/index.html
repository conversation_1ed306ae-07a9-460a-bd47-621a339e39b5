<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#EF4034" />
    <meta
      name="description"
      content="ACME Corporation Developer Portal - Where explosive innovation meets cartoon chaos!"
    />
    <!-- Google Fonts for Acme Corporation Branding -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Luckiest+Guy&family=Bangers&display=swap" rel="stylesheet">
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link
      rel="manifest"
      href="<%= publicPath %>/manifest.json"
      crossorigin="use-credentials"
    />
    <link rel="icon" href="<%= publicPath %>/favicon.ico" />
    <link rel="shortcut icon" href="<%= publicPath %>/favicon.ico" />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="<%= publicPath %>/apple-touch-icon.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="<%= publicPath %>/favicon-32x32.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="<%= publicPath %>/favicon-16x16.png"
    />
    <link
      rel="mask-icon"
      href="<%= publicPath %>/safari-pinned-tab.svg"
      color="#5bbad5"
    />
    <title><%= config.getOptionalString('app.title') ?? 'Backstage' %></title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `yarn start`.
      To create a production bundle, use `yarn build`.
    -->
  </body>
</html>
