#!/bin/bash

# ACME Corporation User Addition Script
# This script helps you quickly add a new user to the Backstage catalog

echo "🎪 Welcome to the ACME Corporation User Addition Tool! 💥"
echo "This script will help you add a new user with Google OAuth support."
echo ""

# Get user input
read -p "Enter the user's name (e.g., john.doe): " username
read -p "Enter the user's display name (e.g., <PERSON>): " displayname
read -p "Enter the user's Google email: " email
read -p "Enter groups (comma-separated, e.g., acme-developers,acme-admins): " groups

# Convert groups to YAML array format
groups_yaml=$(echo "$groups" | sed 's/,/, /g' | sed 's/^/[/' | sed 's/$/]/')

# Create the user YAML
user_yaml="
---
apiVersion: backstage.io/v1alpha1
kind: User
metadata:
  name: $username
  title: $displayname
  annotations:
    google.com/email: $email
spec:
  profile:
    displayName: $displayname
    email: $email
  memberOf: $groups_yaml"

echo ""
echo "🚀 Generated user configuration:"
echo "$user_yaml"
echo ""

# Ask for confirmation
read -p "Add this user to examples/org.yaml? (y/n): " confirm

if [[ $confirm == [yY] || $confirm == [yY][eE][sS] ]]; then
    # Append to org.yaml
    echo "$user_yaml" >> examples/org.yaml
    echo ""
    echo "✅ User added successfully to examples/org.yaml!"
    echo ""
    echo "🎯 Next steps:"
    echo "1. Restart your Backstage server: yarn start"
    echo "2. Wait for the catalog to reload"
    echo "3. Try signing in with Google using: $email"
    echo ""
    echo "💥 ACME Corporation welcomes $displayname to the team!"
else
    echo "❌ User addition cancelled."
    echo "You can manually add the user configuration above to examples/org.yaml"
fi

echo ""
echo "🎪 Thanks for using the ACME Corporation User Addition Tool!"
echo "Remember: With great power comes great responsibility... and occasional explosions! 💣"
