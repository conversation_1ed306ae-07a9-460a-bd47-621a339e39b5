# 🎯 ACME Corporation User Setup Guide

## Quick Fix for "Failed to sign-in" Error

If you're getting the error: **"Failed to sign-in, unable to resolve user identity"**, you need to add your Google email to the user catalog.

### 🚀 Step 1: Add Your Email to the User Catalog

1. **Open the file**: `examples/org.yaml`

2. **Find the InduwaraSMPN user section** (around line 85):
   ```yaml
   ---
   apiVersion: backstage.io/v1alpha1
   kind: User
   metadata:
     name: InduwaraSMPN
     title: Induwara SMPN
     annotations:
       # Add your actual Google email here
       google.com/email: <EMAIL>
   spec:
     profile:
       displayName: Induwara SMPN
       email: <EMAIL>
     memberOf: [acme-developers, acme-admins]
   ```

3. **Replace `<EMAIL>`** with your actual Google email address:
   ```yaml
   annotations:
     google.com/email: <EMAIL>  # Your actual email
   spec:
     profile:
       displayName: Induwara SMPN
       email: <EMAIL>  # Same email here
   ```

### 🎪 Step 2: Add Additional Users (Optional)

To add more users who can sign in with Google:

```yaml
---
apiVersion: backstage.io/v1alpha1
kind: User
metadata:
  name: your.username
  title: Your Display Name
  annotations:
    google.com/email: <EMAIL>
spec:
  profile:
    displayName: Your Display Name
    email: <EMAIL>
    picture: https://avatars.githubusercontent.com/u/YOUR_ID?v=4  # Optional
  memberOf: [acme-developers]  # Choose appropriate groups
```

### 🔄 Step 3: Restart the Application

After making changes to the user catalog:

1. **Stop the server** (Ctrl+C in the terminal)
2. **Restart the server**:
   ```bash
   yarn start
   ```
3. **Wait for the catalog to reload** (you'll see logs about catalog processing)

### 🎯 Step 4: Test Google Sign-In

1. Go to `http://localhost:3000`
2. Click the **Google** sign-in button
3. Authenticate with the Google account that matches the email you added
4. You should now be signed in successfully! 🎉

### 🛠️ Available Groups

Users can be members of these ACME Corporation groups:

- **`acme-developers`**: Main development team
- **`acme-admins`**: Administrative privileges  
- **`guests`**: Limited access for external users

### 💡 Pro Tips

1. **Email Must Match Exactly**: The `google.com/email` annotation must exactly match the email address of your Google account
2. **Multiple Emails**: If you have multiple Google accounts, create separate User entities for each
3. **Display Names**: Use fun ACME-style display names to match the cartoon theme!
4. **Profile Pictures**: Add GitHub avatar URLs or other profile pictures for a personalized experience

### 🎪 Example ACME-Style User

```yaml
---
apiVersion: backstage.io/v1alpha1
kind: User
metadata:
  name: wile.e.coyote
  title: Wile E. Coyote - Super Genius
  annotations:
    google.com/email: <EMAIL>
spec:
  profile:
    displayName: Wile E. Coyote 🐺
    email: <EMAIL>
    bio: "Super genius and chief product tester at ACME Corporation. Specializes in explosive innovations and gravity-defying contraptions."
  memberOf: [acme-developers, acme-admins]
```

### 🚨 Troubleshooting

**Still getting sign-in errors?**

1. Check that your email is spelled correctly in both places
2. Make sure the YAML syntax is valid (proper indentation)
3. Verify you're signing in with the exact same Google account
4. Check the backend logs for any catalog processing errors
5. Try refreshing the catalog: restart the server

**Need help?** The ACME Corporation IT department is standing by! 💥🎯

---

*Remember: At ACME Corporation, we believe that with great authentication comes great responsibility... and occasional cartoon-style explosions!* 🎪
