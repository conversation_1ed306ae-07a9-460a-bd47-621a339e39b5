# ACME Corporation Backstage Environment Variables
# Copy this file to .env and fill in your actual values

# GitHub OAuth (existing)
GITHUB_CLIENT_ID=your_github_client_id_here
GITHUB_CLIENT_SECRET=your_github_client_secret_here

# Google OAuth (new)
AUTH_GOOGLE_CLIENT_ID=your_google_client_id_here
AUTH_GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# Backend Configuration
BACKEND_SECRET=your_backend_secret_here

# Database (if using PostgreSQL)
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_DB=backstage_plugin_catalog

# Other common environment variables
NODE_ENV=development
LOG_LEVEL=info
