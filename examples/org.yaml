---
# ACME Corporation Groups
apiVersion: backstage.io/v1alpha1
kind: Group
metadata:
  name: acme-developers
  title: ACME Developers
  description: The explosive development team at ACME Corporation
spec:
  type: team
  profile:
    displayName: ACME Developers
    email: <EMAIL>
  children: []

---
apiVersion: backstage.io/v1alpha1
kind: Group
metadata:
  name: acme-admins
  title: ACME Administrators
  description: The administrative team keeping ACME's chaos organized
spec:
  type: team
  profile:
    displayName: ACME Administrators
    email: <EMAIL>
  children: []

---
apiVersion: backstage.io/v1alpha1
kind: Group
metadata:
  name: guests
  title: Guest Users
  description: Temporary access for external users
spec:
  type: team
  profile:
    displayName: Guest Users
  children: []

---
# ACME Corporation Users with Google OAuth Support
# https://backstage.io/docs/features/software-catalog/descriptor-format#kind-user

# Example user - replace with your actual Google email
apiVersion: backstage.io/v1alpha1
kind: User
metadata:
  name: john.doe
  title: <PERSON>
  annotations:
    google.com/email: <EMAIL>
spec:
  profile:
    displayName: <PERSON>
    email: <EMAIL>
    picture: https://avatars.githubusercontent.com/u/1?v=4
  memberOf: [acme-developers]

---
# Another example user - replace with your actual Google email
apiVersion: backstage.io/v1alpha1
kind: User
metadata:
  name: jane.smith
  title: Jane Smith
  annotations:
    google.com/email: <EMAIL>
spec:
  profile:
    displayName: Jane Smith
    email: <EMAIL>
    picture: https://avatars.githubusercontent.com/u/2?v=4
  memberOf: [acme-developers, acme-admins]

---
# Guest user (for fallback)
apiVersion: backstage.io/v1alpha1
kind: User
metadata:
  name: guest
  title: Guest User
spec:
  profile:
    displayName: Guest User
  memberOf: [guests]

---
# InduwaraSMPN user with Google OAuth support
apiVersion: backstage.io/v1alpha1
kind: User
metadata:
  name: InduwaraSMPN
  title: Induwara SMPN
  annotations:
    # Add your actual Google email here
    google.com/email: <EMAIL>
spec:
  profile:
    displayName: Induwara SMPN
    email: <EMAIL>
  memberOf: [acme-developers, acme-admins]